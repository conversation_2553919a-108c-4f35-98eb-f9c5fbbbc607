import { Injectable } from '@angular/core';
import { BasicService } from '../basic.service';
import { BasicResponse } from '../data/basic-response';
import { CatalogInfo } from '../data/catalog-info';

@Injectable({
  providedIn: 'root'
})
export class SyncroV2Service extends BasicService {

  constructor() {
    super();
  }

  /**
   * Sincronizza le categorie con paginazione
   * @param idCatalog ID del catalogo
   * @param pageIn Numero della pagina (default: 0)
   * @param pageSize Dimensione della pagina (default: 10)
   * @returns Promise<BasicResponse>
   */
  syncCategories(idCatalog: number, pageIn: number = 0, pageSize: number = 10): Promise<BasicResponse> {
    const url = `/appcatalogs/syncCategories?idCatalog=${idCatalog}&pageIn=${pageIn}&pageSize=${pageSize}`;
    return this.basicGet(url, false);
  }

  /**
   * Sincronizza i prodotti con paginazione
   * @param idCatalog ID del catalogo
   * @param pageIn Numero della pagina (default: 0)
   * @param pageSize Dimensione della pagina (default: 100)
   * @param idRootCategory ID della categoria radice (opzionale)
   * @returns Promise<BasicResponse>
   */
  syncProducts(idCatalog: number, pageIn: number = 0, pageSize: number = 100, idRootCategory?: number): Promise<BasicResponse> {
    const url = `/appcatalogs/syncProducts?idCatalog=${idCatalog}&pageIn=${pageIn}&pageSize=${pageSize}${idRootCategory ? `&idRootCategory=${idRootCategory}` : ''}`;
    return this.basicGet(url, false);
  }

   /**
    * Sincronizza tutti i prodotti con paginazione automatica
    * @param idCatalog ID del catalogo
    * @param pageSize Dimensione della pagina (default: 100)
    * @param idRootCategory ID della categoria radice (opzionale)
    * @param onPageCallback Callback chiamato per ogni pagina processata
    * @returns Promise<BasicResponse[]> Array di tutte le risposte
    */
   async syncAllProducts(
    idCatalog: number,
    pageSize: number = 100,
    idRootCategory?: number,
    onPageCallback?: (pageData: BasicResponse, pageNumber: number, totalPages?: number, totalCount?: number) => void
  ): Promise<BasicResponse[]> {
    const results: BasicResponse[] = [];
    let currentPage = 0;
    let hasMoreData = true;
    let totalPages: number | undefined;
    let totalCount: number | undefined;

    while (hasMoreData) {
      try {
        const response = await this.syncProducts(idCatalog, currentPage, pageSize, idRootCategory);

        if (response && response.status === 'OK') {
          results.push(response);

          // Estrai informazioni dalla struttura della risposta
          if (response.content) {
            // Calcola totalPages e totalCount dalla prima risposta
            if (currentPage === 0 && response.content.totalCount !== undefined) {
              totalCount = response.content.totalCount;
              totalPages = Math.ceil(totalCount / pageSize);
              console.log(`📊 Sincronizzazione categorie: ${totalCount} elementi totali, ${totalPages} pagine da scaricare`);
            }

            // Chiama il callback se fornito
            if (onPageCallback) {
              onPageCallback(response, currentPage, totalPages, totalCount);
            }

            // Verifica se ci sono altre pagine usando totalPages se disponibile
            if (totalPages !== undefined) {
              hasMoreData = currentPage < (totalPages - 1);
            } else {
              // Fallback: usa la logica precedente se totalCount non è disponibile
              const results = response.content.results || response.content;
              if (!Array.isArray(results) || results.length < pageSize) {
                hasMoreData = false;
              }
            }

            if (hasMoreData) {
              currentPage++;
            }
          } else {
            hasMoreData = false;
          }
        } else {
          // In caso di errore, interrompi la sincronizzazione
          hasMoreData = false;
          if (response) {
            results.push(response);
          }
        }
      } catch (error) {
        console.error(`Errore durante la sincronizzazione della pagina ${currentPage}:`, error);
        hasMoreData = false;
      }
    }

    console.log(`✅ Sincronizzazione categorie completata: ${results.length} pagine scaricate`);
    return results;
  }


  /**
   * Sincronizza le categorie cancellate con paginazione e timestamp
   * @param idCatalog ID del catalogo
   * @param pageIn Numero della pagina (default: 0)
   * @param pageSize Dimensione della pagina (default: 10)
   * @param fromTimestamp Timestamp di partenza per la sincronizzazione incrementale
   * @returns Promise<BasicResponse>
   */
  syncDeletedCategories(idCatalog: number, pageIn: number = 0, pageSize: number = 10, fromTimestamp?: number): Promise<BasicResponse> {
    let url = `/appcatalogs/syncDeletedCategories?idCatalog=${idCatalog}&pageIn=${pageIn}&pageSize=${pageSize}`;

    if (fromTimestamp !== undefined && fromTimestamp !== null) {
      url += `&fromTimestamp=${fromTimestamp}`;
    }

    console.log('🔗 SyncroV2Service.syncDeletedCategories - URL:', url);
    return this.basicGet(url, false);
  }

  /**
   * Sincronizza tutte le categorie di un catalogo con gestione automatica della paginazione
   * @param idCatalog ID del catalogo
   * @param pageSize Dimensione della pagina (default: 10)
   * @param onPageCallback Callback chiamata per ogni pagina ricevuta
   * @returns Promise<BasicResponse[]> Array di tutte le risposte delle pagine
   */
  async syncAllCategories(
    idCatalog: number,
    pageSize: number = 10,
    onPageCallback?: (pageData: BasicResponse, pageNumber: number, totalPages?: number, totalCount?: number) => void
  ): Promise<BasicResponse[]> {
    const results: BasicResponse[] = [];
    let currentPage = 0;
    let hasMoreData = true;
    let totalPages: number | undefined;
    let totalCount: number | undefined;

    while (hasMoreData) {
      try {
        const response = await this.syncCategories(idCatalog, currentPage, pageSize);

        if (response && response.status === 'OK') {
          results.push(response);

          // Estrai informazioni dalla struttura della risposta
          if (response.content) {
            // Calcola totalPages e totalCount dalla prima risposta
            if (currentPage === 0 && response.content.totalCount !== undefined) {
              totalCount = response.content.totalCount;
              totalPages = Math.ceil(totalCount / pageSize);
              console.log(`📊 Sincronizzazione categorie: ${totalCount} elementi totali, ${totalPages} pagine da scaricare`);
            }

            // Chiama il callback se fornito
            if (onPageCallback) {
              onPageCallback(response, currentPage, totalPages, totalCount);
            }

            // Verifica se ci sono altre pagine usando totalPages se disponibile
            if (totalPages !== undefined) {
              hasMoreData = currentPage < (totalPages - 1);
            } else {
              // Fallback: usa la logica precedente se totalCount non è disponibile
              const results = response.content.results || response.content;
              if (!Array.isArray(results) || results.length < pageSize) {
                hasMoreData = false;
              }
            }

            if (hasMoreData) {
              currentPage++;
            }
          } else {
            hasMoreData = false;
          }
        } else {
          // In caso di errore, interrompi la sincronizzazione
          hasMoreData = false;
          if (response) {
            results.push(response);
          }
        }
      } catch (error) {
        console.error(`Errore durante la sincronizzazione della pagina ${currentPage}:`, error);
        hasMoreData = false;
      }
    }

    console.log(`✅ Sincronizzazione categorie completata: ${results.length} pagine scaricate`);
    return results;
  }

  /**
   * Sincronizza tutte le categorie cancellate di un catalogo con gestione automatica della paginazione
   * @param idCatalog ID del catalogo
   * @param fromTimestamp Timestamp di partenza per la sincronizzazione incrementale
   * @param pageSize Dimensione della pagina (default: 10)
   * @param onPageCallback Callback chiamata per ogni pagina ricevuta
   * @returns Promise<BasicResponse[]> Array di tutte le risposte delle pagine
   */
  async syncAllDeletedCategories(
    idCatalog: number,
    fromTimestamp?: number,
    pageSize: number = 10,
    onPageCallback?: (pageData: BasicResponse, pageNumber: number, totalPages?: number, totalCount?: number) => void
  ): Promise<BasicResponse[]> {
    const results: BasicResponse[] = [];
    let currentPage = 0;
    let hasMoreData = true;
    let totalPages: number | undefined;
    let totalCount: number | undefined;

    while (hasMoreData) {
      try {
        const response = await this.syncDeletedCategories(idCatalog, currentPage, pageSize, fromTimestamp);

        if (response && response.status === 'OK') {
          results.push(response);

          // Estrai informazioni dalla struttura della risposta
          if (response.content) {
            // Calcola totalPages e totalCount dalla prima risposta
            if (currentPage === 0 && response.content.totalCount !== undefined) {
              totalCount = response.content.totalCount;
              totalPages = Math.ceil(totalCount / pageSize);
              console.log(`📊 Sincronizzazione categorie cancellate: ${totalCount} elementi totali, ${totalPages} pagine da scaricare`);
            }

            // Chiama il callback se fornito
            if (onPageCallback) {
              onPageCallback(response, currentPage, totalPages, totalCount);
            }

            // Verifica se ci sono altre pagine usando totalPages se disponibile
            if (totalPages !== undefined) {
              hasMoreData = currentPage < (totalPages - 1);
            } else {
              // Fallback: usa la logica precedente se totalCount non è disponibile
              const results = response.content.results || response.content;
              if (!Array.isArray(results) || results.length < pageSize) {
                hasMoreData = false;
              }
            }

            if (hasMoreData) {
              currentPage++;
            }
          } else {
            hasMoreData = false;
          }
        } else {
          // In caso di errore, interrompi la sincronizzazione
          hasMoreData = false;
          if (response) {
            results.push(response);
          }
        }
      } catch (error) {
        console.error(`Errore durante la sincronizzazione delle categorie cancellate della pagina ${currentPage}:`, error);
        hasMoreData = false;
      }
    }

    console.log(`✅ Sincronizzazione categorie cancellate completata: ${results.length} pagine scaricate`);
    return results;
  }

  /**
   * Ottiene le informazioni del catalogo basate sulla lingua
   * @param lang Codice della lingua (es: 'it', 'en', 'de')
   * @returns Promise<BasicResponse> Risposta contenente le informazioni del catalogo
   */
  getCatalogByLang(lang: string): Promise<BasicResponse> {
    const url = `/appcatalogs/getCatalogByLang?lang=${lang}`;
    console.log('🔗 SyncroV2Service.getCatalogByLang - URL:', url);
    return this.basicGet(url, false);
  }

  /**
   * Ottiene la gerarchia dei settori per un catalogo
   *
   * NOTA: Questo metodo è utilizzato SOLO dal processo di sincronizzazione Sync v2.
   * I componenti dell'applicazione devono utilizzare i dati dal database locale
   * tramite TagFilterService.getSectorHierarchyFromDatabase().
   *
   * @param idCatalog ID del catalogo
   * @returns Promise<BasicResponse> Risposta contenente la gerarchia dei settori
   */
  getSectorHierarchy(idCatalog: number): Promise<BasicResponse> {
    const url = `/appcatalogs/getSectorHierarchy?idCatalog=${idCatalog}`;
    return this.basicGet(url, false);
  }

  /**
   * Sincronizza i tag DC per le root category
   *
   * NOTA: Questo metodo è utilizzato SOLO dal processo di sincronizzazione Sync v2.
   * I componenti dell'applicazione devono utilizzare i dati dal database locale
   * tramite TagFilterService.getCachedCategoryTags().
   *
   * @param idCatalog ID del catalogo
   * @param pageIn Numero della pagina (default: 0)
   * @param pageSize Dimensione della pagina (default: 500)
   * @param fromTimestamp Timestamp da cui iniziare la sincronizzazione (opzionale)
   * @returns Promise<BasicResponse> Risposta contenente i tag DC
   */
  syncDCTags(idCatalog: number, pageIn: number = 0, pageSize: number = 500, fromTimestamp?: number): Promise<BasicResponse> {
    let url = `/appcatalogs/syncDCTags?idCatalog=${idCatalog}&pageIn=${pageIn}&pageSize=${pageSize}`;
    if (fromTimestamp) {
      url += `&fromTimestamp=${fromTimestamp}`;
    }
    return this.basicGet(url, false);
  }

  /**
   * Sincronizza tutti i tag DC con paginazione automatica
   * @param idCatalog ID del catalogo
   * @param pageSize Dimensione della pagina (default: 500)
   * @param fromTimestamp Timestamp da cui iniziare la sincronizzazione (opzionale)
   * @param onPageCallback Callback chiamato per ogni pagina processata
   * @returns Promise<BasicResponse[]> Array di tutte le risposte
   */
  async syncAllDCTags(
    idCatalog: number,
    pageSize: number = 500,
    fromTimestamp?: number,
    onPageCallback?: (pageData: BasicResponse, pageNumber: number, totalPages?: number, totalCount?: number) => void
  ): Promise<BasicResponse[]> {
    const results: BasicResponse[] = [];
    let currentPage = 0;
    let hasMoreData = true;
    let totalPages: number | undefined;
    let totalCount: number | undefined;

    while (hasMoreData) {
      try {
        const response = await this.syncDCTags(idCatalog, currentPage, pageSize, fromTimestamp);

        if (response && response.status === 'OK') {
          results.push(response);

          // Estrai informazioni dalla struttura della risposta
          if (response.content) {
            // Calcola totalPages e totalCount dalla prima risposta
            if (currentPage === 0 && response.content.totalCount !== undefined) {
              totalCount = response.content.totalCount;
              totalPages = Math.ceil(totalCount / pageSize);
              console.log(`📊 Sincronizzazione tag DC: ${totalCount} elementi totali, ${totalPages} pagine da scaricare`);
            }

            // Chiama il callback se fornito
            if (onPageCallback) {
              onPageCallback(response, currentPage, totalPages, totalCount);
            }

            // Verifica se ci sono altre pagine usando totalPages se disponibile
            if (totalPages !== undefined) {
              hasMoreData = currentPage < (totalPages - 1);
            } else {
              // Fallback: usa la logica precedente se totalCount non è disponibile
              const results = response.content.results || response.content;
              if (!Array.isArray(results) || results.length < pageSize) {
                hasMoreData = false;
              }
            }
          } else {
            hasMoreData = false;
          }

          currentPage++;
        } else {
          console.error(`❌ Errore nella sincronizzazione tag DC pagina ${currentPage}:`, response?.error || 'Risposta non valida');
          hasMoreData = false;
        }
      } catch (error) {
        console.error(`❌ Errore nella sincronizzazione tag DC pagina ${currentPage}:`, error);
        hasMoreData = false;
      }
    }

    console.log(`✅ Sincronizzazione tag DC completata: ${results.length} pagine processate`);
    return results;
  }

  /**
   * Sincronizza tutti i clienti dell'utente
   *
   * NOTA: Questo metodo è utilizzato SOLO dal processo di sincronizzazione Sync v2.
   * I componenti dell'applicazione devono utilizzare i dati dal database locale.
   *
   * @param fromTimestamp Timestamp da cui iniziare la sincronizzazione (opzionale)
   * @returns Promise<BasicResponse> Risposta contenente i clienti
   */
  allCustomersByUser(fromTimestamp?: number): Promise<BasicResponse> {
    const incr = fromTimestamp ? `?fromTimestamp=${fromTimestamp}` : '';
    return this.basicGet(`/customers/getsync/allcustomersbyuser${incr}`, false);
  }

}
